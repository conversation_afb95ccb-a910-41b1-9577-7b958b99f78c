#include "TrayManager.h"
#include "DlgMain_MonitorWhiteCtrlProgram.h"
#include <QIcon>
#include <QPixmap>
#include <QPainter>
#include <QFont>

TrayManager::TrayManager(CDlgMain_MonitorWhiteCtrlProgram *mainWindow, QObject *parent)
    : QObject(parent)
    , m_mainWindow(mainWindow)
    , m_trayIcon(nullptr)
    , m_trayMenu(nullptr)
    , m_currentStatus("就绪")
    , m_whiteListCount(0)
{
    if (isSystemTrayAvailable()) {
        createTrayIcon();
        createTrayMenu();
        showTrayIcon();
        
        qDebug() << "系统托盘初始化完成";
    } else {
        qWarning() << "系统不支持托盘功能";
    }
}

TrayManager::~TrayManager()
{
    if (m_trayIcon) {
        m_trayIcon->hide();
        delete m_trayIcon;
    }
    
    if (m_trayMenu) {
        delete m_trayMenu;
    }
}

bool TrayManager::isSystemTrayAvailable()
{
    return QSystemTrayIcon::isSystemTrayAvailable();
}

void TrayManager::createTrayIcon()
{
    m_trayIcon = new QSystemTrayIcon(this);
    
    // 创建托盘图标
    QPixmap pixmap(32, 32);
    pixmap.fill(Qt::transparent);
    
    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 绘制显示器图标
    painter.setBrush(QBrush(QColor(70, 130, 180)));
    painter.setPen(QPen(QColor(25, 25, 112), 2));
    painter.drawRoundedRect(4, 6, 24, 16, 2, 2);
    
    // 绘制显示器底座
    painter.drawRect(14, 22, 4, 4);
    painter.drawRect(10, 26, 12, 2);
    
    // 绘制状态指示点
    painter.setBrush(QBrush(QColor(0, 255, 0)));
    painter.setPen(Qt::NoPen);
    painter.drawEllipse(26, 8, 4, 4);
    
    QIcon icon(pixmap);
    m_trayIcon->setIcon(icon);
    
    // 设置工具提示
    updateTrayStatus(m_currentStatus);
    
    // 连接信号
    connect(m_trayIcon, &QSystemTrayIcon::activated,
            this, &TrayManager::onTrayIconActivated);
}

void TrayManager::createTrayMenu()
{
    m_trayMenu = new QMenu();
    
    // 创建菜单项
    m_showAction = new QAction("显示主窗口", this);
    m_showAction->setIcon(QIcon(":/icons/show.png"));
    connect(m_showAction, &QAction::triggered, this, &TrayManager::onShowMainWindow);
    
    m_hideAction = new QAction("隐藏主窗口", this);
    m_hideAction->setIcon(QIcon(":/icons/hide.png"));
    connect(m_hideAction, &QAction::triggered, this, &TrayManager::onHideMainWindow);
    
    m_clearWhiteListAction = new QAction("清空白名单", this);
    m_clearWhiteListAction->setIcon(QIcon(":/icons/clear.png"));
    connect(m_clearWhiteListAction, &QAction::triggered, this, &TrayManager::onClearWhiteList);
    
    m_syncWhiteListAction = new QAction("同步白名单", this);
    m_syncWhiteListAction->setIcon(QIcon(":/icons/sync.png"));
    connect(m_syncWhiteListAction, &QAction::triggered, this, &TrayManager::onSyncWhiteList);
    
    m_aboutAction = new QAction("关于", this);
    m_aboutAction->setIcon(QIcon(":/icons/about.png"));
    connect(m_aboutAction, &QAction::triggered, this, &TrayManager::onAbout);
    
    m_exitAction = new QAction("退出", this);
    m_exitAction->setIcon(QIcon(":/icons/exit.png"));
    connect(m_exitAction, &QAction::triggered, this, &TrayManager::onExit);
    
    // 添加菜单项
    m_trayMenu->addAction(m_showAction);
    m_trayMenu->addAction(m_hideAction);
    m_trayMenu->addSeparator();
    m_trayMenu->addAction(m_clearWhiteListAction);
    m_trayMenu->addAction(m_syncWhiteListAction);
    m_trayMenu->addSeparator();
    m_trayMenu->addAction(m_aboutAction);
    m_trayMenu->addSeparator();
    m_trayMenu->addAction(m_exitAction);
    
    // 设置托盘菜单
    m_trayIcon->setContextMenu(m_trayMenu);
}

void TrayManager::showTrayIcon()
{
    if (m_trayIcon && isSystemTrayAvailable()) {
        m_trayIcon->show();
        qDebug() << "托盘图标已显示";
    }
}

void TrayManager::hideTrayIcon()
{
    if (m_trayIcon) {
        m_trayIcon->hide();
        qDebug() << "托盘图标已隐藏";
    }
}

void TrayManager::updateTrayStatus(const QString &status)
{
    m_currentStatus = status;
    
    if (m_trayIcon) {
        QString tooltip = QString("显示器管控程序\n状态: %1\n白名单: %2 个显示器")
                         .arg(status)
                         .arg(m_whiteListCount);
        m_trayIcon->setToolTip(tooltip);
    }
}

void TrayManager::onTrayIconActivated(QSystemTrayIcon::ActivationReason reason)
{
    switch (reason) {
    case QSystemTrayIcon::DoubleClick:
        // 双击显示主窗口
        onShowMainWindow();
        break;
    case QSystemTrayIcon::Trigger:
        // 单击切换窗口显示状态
        if (m_mainWindow) {
            if (m_mainWindow->isVisible()) {
                onHideMainWindow();
            } else {
                onShowMainWindow();
            }
        }
        break;
    default:
        break;
    }
}

void TrayManager::onShowMainWindow()
{
    if (m_mainWindow) {
        m_mainWindow->show();
        m_mainWindow->raise();
        m_mainWindow->activateWindow();
        qDebug() << "主窗口已显示";
    }
}

void TrayManager::onHideMainWindow()
{
    if (m_mainWindow) {
        m_mainWindow->hide();
        showMessage("显示器管控程序", "程序已最小化到系统托盘");
        qDebug() << "主窗口已隐藏到托盘";
    }
}

void TrayManager::onClearWhiteList()
{
    int ret = QMessageBox::question(nullptr, "确认操作", 
                                   "确定要清空显示器白名单吗？\n此操作不可撤销。",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        emit clearWhiteListRequested();
        showMessage("白名单管理", "显示器白名单已清空");
        qDebug() << "用户请求清空白名单";
    }
}

void TrayManager::onSyncWhiteList()
{
    showMessage("白名单管理", "正在同步显示器白名单...");
    emit syncWhiteListRequested();
    qDebug() << "用户请求同步白名单";
}

void TrayManager::onAbout()
{
    QString aboutText = QString(
        "<h3>显示器管控程序 v1.0</h3>"
        "<p><b>功能特性：</b></p>"
        "<ul>"
        "<li>实时监控显示器EDID信息</li>"
        "<li>显示器白名单管理</li>"
        "<li>USB Key白名单同步</li>"
        "<li>系统服务模式运行</li>"
        "<li>开机自动启动</li>"
        "</ul>"
        "<p><b>技术规格：</b></p>"
        "<ul>"
        "<li>基于Qt 5.6.3框架</li>"
        "<li>Windows API集成</li>"
        "<li>多线程架构</li>"
        "<li>1秒监控间隔</li>"
        "</ul>"
        "<p><b>版权信息：</b></p>"
        "<p>© 2024 显示器管控程序<br>"
        "构建时间: %1 %2</p>"
    ).arg(__DATE__).arg(__TIME__);
    
    QMessageBox::about(nullptr, "关于显示器管控程序", aboutText);
}

void TrayManager::onExit()
{
    int ret = QMessageBox::question(nullptr, "确认退出", 
                                   "确定要退出显示器管控程序吗？",
                                   QMessageBox::Yes | QMessageBox::No,
                                   QMessageBox::No);
    
    if (ret == QMessageBox::Yes) {
        qDebug() << "用户请求退出程序";
        QApplication::quit();
    }
}

void TrayManager::onWhiteListUpdated(int count)
{
    m_whiteListCount = count;
    updateTrayStatus(m_currentStatus);
    
    showMessage("白名单更新", QString("白名单已更新，当前包含 %1 个显示器").arg(count));
}

void TrayManager::showMessage(const QString &title, const QString &message, 
                             QSystemTrayIcon::MessageIcon icon)
{
    if (m_trayIcon && isSystemTrayAvailable()) {
        m_trayIcon->showMessage(title, message, icon, 3000);
    }
}
