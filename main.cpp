#include "DlgMain_MonitorWhiteCtrlProgram.h"
#include <QApplication>
#include <QDebug>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // 设置应用程序信息
    a.setApplicationName("Monitor Control Program");
    a.setApplicationVersion("1.0");
    a.setOrganizationName("MonitorControl");

    qDebug() << "========================================";
    qDebug() << "Monitor Control Program Started";
    qDebug() << "Version: 1.0";
    qDebug() << "Build Time:" << __DATE__ << __TIME__;
    qDebug() << "========================================";

    // 创建并显示主窗口
    CDlgMain_MonitorWhiteCtrlProgram w;
    w.show();

    qDebug() << "主窗口已显示，开始事件循环...";

    return a.exec();
}
