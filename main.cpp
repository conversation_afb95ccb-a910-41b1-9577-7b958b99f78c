#include "DlgMain_MonitorWhiteCtrlProgram.h"
#include <QApplication>
#include <QDebug>
#include <QTextCodec>

#ifdef Q_OS_WIN
#include <windows.h>
#include <iostream>
#endif

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // ===============================
    // 解决QtCreator控制台中文乱码问题
    // ===============================

#ifdef Q_OS_WIN
    // Windows平台控制台编码设置

    // 1. 设置控制台代码页为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // 2. 跳过_setmode设置，避免"Invalid parameter"错误
    // 注意：_setmode在某些环境下可能导致C运行时错误
    // 我们的自定义消息处理器已经足够处理编码转换

    // 3. 跳过locale设置，避免程序崩溃
    // 注意：locale设置在某些Windows系统上可能导致程序异常退出
    // 我们的自定义消息处理器已经足够处理中文编码问题
#endif

    // 设置应用程序信息
    a.setApplicationName("显示器管控程序");
    a.setApplicationVersion("1.0");
    a.setOrganizationName("MonitorControl");

    // 设置Qt的文本编码（Qt 5.6.3兼容）
    QTextCodec *utf8Codec = QTextCodec::codecForName("UTF-8");
    if (utf8Codec) {
        QTextCodec::setCodecForLocale(utf8Codec);
        // 注意：setCodecForCStrings在Qt 5.x中已被弃用，不再使用
    }

    // 安装自定义消息处理器，确保qDebug中文正确输出到QtCreator
    qInstallMessageHandler([](QtMsgType type, const QMessageLogContext &context, const QString &msg) {
        Q_UNUSED(context)

        // 构建消息前缀
        QString prefix;
        switch (type) {
        case QtDebugMsg:    prefix = "Debug: "; break;
        case QtWarningMsg:  prefix = "Warning: "; break;
        case QtCriticalMsg: prefix = "Critical: "; break;
        case QtFatalMsg:    prefix = "Fatal: "; break;
        }

        // 完整消息
        QString fullMsg = prefix + msg;

#ifdef Q_OS_WIN
        // Windows平台：多种方式确保中文正确显示

        // 方法1：直接输出到控制台（如果有控制台窗口）
        HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
        if (hConsole != INVALID_HANDLE_VALUE) {
            // 转换为宽字符并输出
            std::wstring wstr = fullMsg.toStdWString();
            wstr += L"\r\n";  // Windows换行符
            DWORD written;
            WriteConsoleW(hConsole, wstr.c_str(), static_cast<DWORD>(wstr.length()), &written, nullptr);
        }

        // 方法2：输出UTF-8到stderr（QtCreator主要捕获这个）
        QByteArray utf8Data = fullMsg.toUtf8();

        // 添加UTF-8 BOM前缀，帮助QtCreator识别编码
        static bool firstOutput = true;
        if (firstOutput) {
            fprintf(stderr, "\xEF\xBB\xBF");  // UTF-8 BOM
            firstOutput = false;
        }

        fprintf(stderr, "%s\n", utf8Data.constData());
        fflush(stderr);

        // 方法3：同时输出到stdout（备用）
        fprintf(stdout, "%s\n", utf8Data.constData());
        fflush(stdout);

#else
        // 非Windows平台
        QByteArray utf8Data = fullMsg.toUtf8();
        fprintf(stderr, "%s\n", utf8Data.constData());
        fflush(stderr);
#endif
    });

    qDebug() << "========================================";
    qDebug() << QString::fromUtf8("\xe6\x98\xbe\xe7\xa4\xba\xe5\x99\xa8\xe7\xae\xa1\xe6\x8e\xa7\xe7\xa8\x8b\xe5\xba\x8f\xe5\x90\xaf\xe5\x8a\xa8");
    qDebug() << QString::fromUtf8("\xe7\x89\x88\xe6\x9c\xac: 1.0");
    qDebug() << QString::fromUtf8("\xe6\x9e\x84\xe5\xbb\xba\xe6\x97\xb6\xe9\x97\xb4:") << __DATE__ << __TIME__;
    qDebug() << "========================================";
    qDebug() << QString::fromUtf8("\xe6\xb5\x8b\xe8\xaf\x95\xe4\xb8\xad\xe6\x96\x87\xe8\xbe\x93\xe5\x87\xba\xef\xbc\x9a\xe8\xbf\x99\xe6\x98\xaf\xe4\xb8\x80\xe6\x9d\xa1\xe5\x8c\x85\xe5\x90\xab\xe4\xb8\xad\xe6\x96\x87\xe7\x9a\x84\xe6\xb5\x8b\xe8\xaf\x95\xe6\xb6\x88\xe6\x81\xaf");
    qDebug() << QString::fromUtf8("\xe7\x89\xb9\xe6\xae\x8a\xe5\xad\x97\xe7\xac\xa6\xe6\xb5\x8b\xe8\xaf\x95\xef\xbc\x9a\xe2\x91\xa0\xe2\x91\xa1\xe2\x91\xa2\xe2\x91\xa3\xe2\x91\xa4 \xe2\x98\x85\xe2\x98\x86\xe2\x99\xa0\xe2\x99\xa5\xe2\x99\xa6\xe2\x99\xa3 \xce\xb1\xce\xb2\xce\xb3\xce\xb4\xce\xb5");

    // 创建并显示主窗口
    CDlgMain_MonitorWhiteCtrlProgram w;
    w.show();

    qDebug() << "主窗口已显示，开始事件循环...";

    return a.exec();
}
