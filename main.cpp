#include "DlgMain_MonitorWhiteCtrlProgram.h"
#include <QApplication>
#include <QDebug>
#include <QTextCodec>

#ifdef Q_OS_WIN
#include <windows.h>
#include <iostream>
#endif

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // ===============================
    // 解决QtCreator控制台中文乱码问题
    // ===============================

#ifdef Q_OS_WIN
    // Windows平台控制台编码设置

    // 1. 设置控制台代码页为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // 2. 跳过_setmode设置，避免"Invalid parameter"错误
    // 注意：_setmode在某些环境下可能导致C运行时错误
    // 我们的自定义消息处理器已经足够处理编码转换

    // 3. 跳过locale设置，避免程序崩溃
    // 注意：locale设置在某些Windows系统上可能导致程序异常退出
    // 我们的自定义消息处理器已经足够处理中文编码问题
#endif

    // 设置应用程序信息
    a.setApplicationName("显示器管控程序");
    a.setApplicationVersion("1.0");
    a.setOrganizationName("MonitorControl");

    // 设置Qt的文本编码（Qt 5.6.3兼容）
    QTextCodec *utf8Codec = QTextCodec::codecForName("UTF-8");
    if (utf8Codec) {
        QTextCodec::setCodecForLocale(utf8Codec);
        // 注意：setCodecForCStrings在Qt 5.x中已被弃用，不再使用
    }

    // 安装自定义消息处理器，确保qDebug中文正确输出到QtCreator
    qInstallMessageHandler([](QtMsgType type, const QMessageLogContext &context, const QString &msg) {
        Q_UNUSED(context)

        QString txt;
        switch (type) {
        case QtDebugMsg:    txt = QString("Debug: %1").arg(msg); break;
        case QtWarningMsg:  txt = QString("Warning: %1").arg(msg); break;
        case QtCriticalMsg: txt = QString("Critical: %1").arg(msg); break;
        case QtFatalMsg:    txt = QString("Fatal: %1").arg(msg); break;
        }

#ifdef Q_OS_WIN
        // Windows平台：同时输出到控制台和标准错误流

        // 方法1：使用WriteConsoleW输出到控制台
        HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
        if (hConsole != INVALID_HANDLE_VALUE) {
            std::wstring wstr = txt.toStdWString();
            wstr += L"\n";
            DWORD written;
            WriteConsoleW(hConsole, wstr.c_str(), static_cast<DWORD>(wstr.length()), &written, nullptr);
        }

        // 方法2：输出到标准错误流（QtCreator会捕获这个）
        QByteArray utf8Data = txt.toUtf8();
        fprintf(stderr, "%s\n", utf8Data.constData());
        fflush(stderr);

#else
        // 非Windows平台直接输出到标准错误流
        QByteArray utf8Data = txt.toUtf8();
        fprintf(stderr, "%s\n", utf8Data.constData());
        fflush(stderr);
#endif
    });

    qDebug() << "========================================";
    qDebug() << "显示器管控程序启动";
    qDebug() << "版本: 1.0";
    qDebug() << "构建时间:" << __DATE__ << __TIME__;
    qDebug() << "========================================";
    qDebug() << "测试中文输出：这是一条包含中文的测试消息";
    qDebug() << "特殊字符测试：①②③④⑤ ★☆♠♥♦♣ αβγδε";

    // 创建并显示主窗口
    CDlgMain_MonitorWhiteCtrlProgram w;
    w.show();

    qDebug() << "主窗口已显示，开始事件循环...";

    return a.exec();
}
