#include "DlgMain_MonitorWhiteCtrlProgram.h"
#include <QApplication>
#include <QDebug>
#include <QTextCodec>

#ifdef Q_OS_WIN
#include <windows.h>
#include <iostream>
#endif

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // 简化的控制台设置（仅用于英文调试信息）
#ifdef Q_OS_WIN
    // 设置控制台代码页为UTF-8（可选）
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif

    // 设置应用程序信息
    a.setApplicationName("显示器管控程序");
    a.setApplicationVersion("1.0");
    a.setOrganizationName("MonitorControl");

    // 设置Qt的文本编码（Qt 5.6.3兼容）
    QTextCodec *utf8Codec = QTextCodec::codecForName("UTF-8");
    if (utf8Codec) {
        QTextCodec::setCodecForLocale(utf8Codec);
        // 注意：setCodecForCStrings在Qt 5.x中已被弃用，不再使用
    }

    // 简化的消息处理器，用于英文调试信息
    qInstallMessageHandler([](QtMsgType type, const QMessageLogContext &context, const QString &msg) {
        Q_UNUSED(context)

        // 构建消息前缀
        QString prefix;
        switch (type) {
        case QtDebugMsg:    prefix = "Debug: "; break;
        case QtWarningMsg:  prefix = "Warning: "; break;
        case QtCriticalMsg: prefix = "Critical: "; break;
        case QtFatalMsg:    prefix = "Fatal: "; break;
        }

        // 完整消息
        QString fullMsg = prefix + msg;

        // 直接输出到stderr（QtCreator捕获）
        QByteArray utf8Data = fullMsg.toUtf8();
        fprintf(stderr, "%s\n", utf8Data.constData());
        fflush(stderr);
    });

    qDebug() << "========================================";
    qDebug() << "Monitor Control Program Started";
    qDebug() << "Version: 1.0";
    qDebug() << "Build Time:" << __DATE__ << __TIME__;
    qDebug() << "========================================";

    // 创建并显示主窗口
    CDlgMain_MonitorWhiteCtrlProgram w;
    w.show();

    qDebug() << "主窗口已显示，开始事件循环...";

    return a.exec();
}
