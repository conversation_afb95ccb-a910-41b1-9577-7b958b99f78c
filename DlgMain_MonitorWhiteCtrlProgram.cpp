#include "DlgMain_MonitorWhiteCtrlProgram.h"
#include "ui_DlgMain_MonitorWhiteCtrlProgram.h"
#include "TrayManager.h"
#include "WhiteListManager.h"
#include "USBDeviceMonitor.h"
#include "ServiceManager.h"
#include <QApplication>
#include <QMessageBox>
#include <QInputDialog>
#include <QMutexLocker>
#include <QFileInfo>
#include <QDir>
#include <QCoreApplication>

// Windows显示器枚举回调函数
#ifdef Q_OS_WIN
BOOL CALLBACK MonitorEnumProc(HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData)
{
    Q_UNUSED(hdcMonitor)

    MONITORINFOEX monitorInfo;
    monitorInfo.cbSize = sizeof(MONITORINFOEX);

    // 获取显示器详细信息
    if (GetMonitorInfo(hMonitor, &monitorInfo)) {
        QString deviceName = QString::fromWCharArray(monitorInfo.szDevice);

        qDebug() << "Monitor device:" << deviceName;
        qDebug() << "  Position: (" << lprcMonitor->left << ", " << lprcMonitor->top << ")";
        qDebug() << "  Size:" << (lprcMonitor->right - lprcMonitor->left) << " x "
                  << (lprcMonitor->bottom - lprcMonitor->top) << " pixels";
        qDebug() << "  Is primary:" << (monitorInfo.dwFlags & MONITORINFOF_PRIMARY ? "Yes" : "No");

        // 将显示器信息传递给调用者
        if (dwData) {
            QList<DisplayInfo> *displaysList = reinterpret_cast<QList<DisplayInfo>*>(dwData);

            // 查找对应的EDID信息
            for (DisplayInfo &info : *displaysList) {
                if (info.deviceName.contains(deviceName) || deviceName.contains(info.deviceName)) {
                    info.isPrimary = (monitorInfo.dwFlags & MONITORINFOF_PRIMARY) != 0;
                    break;
                }
            }
        }
    }
    return TRUE; // 返回 TRUE 继续枚举，返回 FALSE 停止枚举
}
#endif

// ===============================
// 主窗口类实现
// ===============================

CDlgMain_MonitorWhiteCtrlProgram::CDlgMain_MonitorWhiteCtrlProgram(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::CDlgMain_MonitorWhiteCtrlProgram),
    m_edidManager(nullptr),
    m_trayManager(nullptr),
    m_whiteListManager(nullptr),
    m_usbDeviceMonitor(nullptr),
    m_serviceManager(nullptr),
    m_isInitialized(false)
{
    ui->setupUi(this);

    // 设置窗口标题
    setWindowTitle("显示器管控程序 v1.0");

    // 记录程序启动
    WriteLog("显示器管控程序启动");
    WriteLog("测试中文编码：这是一条包含中文的测试日志");
    WriteLog("特殊字符测试：①②③④⑤ ★☆♠♥♦♣ αβγδε");

    // 按顺序初始化所有管理器
    initEDIDManager();
    initWhiteListManager();
    initUSBDeviceMonitor();
    initServiceManager();
    initTrayManager();

    // 显示所有显示器信息
    showDisplaysInfo();

    // 更新窗口标题
    updateWindowTitle();

    m_isInitialized = true;

    qDebug() << "Main window initialization completed";
}

CDlgMain_MonitorWhiteCtrlProgram::~CDlgMain_MonitorWhiteCtrlProgram()
{
    WriteLog("显示器管控程序退出");

    // 按相反顺序清理管理器
    if (m_trayManager) {
        delete m_trayManager;
        m_trayManager = nullptr;
    }

    if (m_usbDeviceMonitor) {
        delete m_usbDeviceMonitor;
        m_usbDeviceMonitor = nullptr;
    }

    if (m_whiteListManager) {
        delete m_whiteListManager;
        m_whiteListManager = nullptr;
    }

    if (m_serviceManager) {
        delete m_serviceManager;
        m_serviceManager = nullptr;
    }

    if (m_edidManager) {
        delete m_edidManager;
        m_edidManager = nullptr;
    }

    delete ui;
}

void CDlgMain_MonitorWhiteCtrlProgram::initEDIDManager()
{
    m_edidManager = new EDIDManager(this);

    // 连接信号槽
    connect(m_edidManager, &EDIDManager::displaysChanged,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onDisplaysChanged);
}

void CDlgMain_MonitorWhiteCtrlProgram::refreshDisplaysInfo()
{
    if (m_edidManager) {
        m_edidManager->refreshDisplaysInfo();
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::showDisplaysInfo()
{
    if (!m_edidManager) return;

    QList<DisplayInfo> displays = m_edidManager->getAllDisplaysInfo();

    qDebug() << "\n========== Monitor EDID Information ==========";
    qDebug() << "Detected" << displays.size() << "monitors:";

    for (int i = 0; i < displays.size(); ++i) {
        const DisplayInfo &info = displays[i];

        qDebug() << "\n--- Monitor" << (i + 1) << "---";
        qDebug() << "Device Name:" << info.deviceName;
        qDebug() << "Manufacturer:" << info.manufacturer;
        qDebug() << "Product Code:" << info.productCode;
        qDebug() << "Serial Number:" << info.serialNumber;
        qDebug() << "Manufacture Date:" << "Week" << info.manufactureWeek << "," << info.manufactureYear;
        qDebug() << "EDID Version:" << info.edidVersion;
        qDebug() << "Physical Size:" << info.physicalSize.width() << "x" << info.physicalSize.height() << "mm";
        qDebug() << "Native Resolution:" << info.nativeResolution.width() << "x" << info.nativeResolution.height();
        qDebug() << "Is Primary:" << (info.isPrimary ? "Yes" : "No");

        qDebug() << "Supported Resolutions:";
        for (const QSize &res : info.supportedResolutions) {
            qDebug() << "  " << res.width() << "x" << res.height();
        }

        qDebug() << "Raw EDID Data (first 32 bytes):" << info.rawEDID.left(32).toHex();

        // 记录日志
        WriteLog(QString("检测到显示器: %1 (%2 %3)")
                .arg(info.deviceName)
                .arg(info.manufacturer)
                .arg(info.productCode));
    }

    qDebug() << "=============================================\n";
}

void CDlgMain_MonitorWhiteCtrlProgram::onDisplaysChanged()
{
    qDebug() << "Monitor configuration changed, reloading information...";
    showDisplaysInfo();

    WriteLog("显示器配置发生变化");
}

void CDlgMain_MonitorWhiteCtrlProgram::WriteLog(const QString &message)
{
    QMutexLocker locker(&m_mutex_Log);  // 使用RAII方式管理锁

    if (!m_file_Log.isOpen())
    {
        // 获取程序所在目录
        QString appDirPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath();
        QString logDirPath = QDir(appDirPath).filePath("Log");

        // 检查Log文件夹是否存在，不存在则创建
        QDir logDir(logDirPath);
        if (!logDir.exists()) {
            if (logDir.mkpath(logDirPath)) {
                qDebug() << "Log directory created:" << logDirPath;
            } else {
                qWarning() << "Failed to create log directory:" << logDirPath;
                // 如果创建失败，使用程序目录作为备选
                logDirPath = appDirPath;
            }
        } else {
            qDebug() << "Log directory exists:" << logDirPath;
        }

        // 生成日志文件名（包含时间戳以避免冲突）
        QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd");
        QString logFileName = QString("%1_MonitorCtrl.log").arg(timestamp);
        QString logFilePath = QDir(logDirPath).filePath(logFileName);

        // 设置日志文件路径并打开
        m_file_Log.setFileName(logFilePath);
        if (m_file_Log.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
            m_textStream_Out.setDevice(&m_file_Log);

            // 写入日志文件头信息
            m_textStream_Out << "========================================\n";
            m_textStream_Out << "Monitor Control Program Log File\n";
            m_textStream_Out << "Start Time: " << QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss.zzz") << "\n";
            m_textStream_Out << "Program Version: v1.0\n";
            m_textStream_Out << "User Name: " << getSystemUserName() << "\n";
            m_textStream_Out << "Log File: " << logFilePath << "\n";
            m_textStream_Out << "========================================\n";
            m_textStream_Out.flush();

            qDebug() << "Log file created successfully:" << logFilePath;
        } else {
            qWarning() << "Failed to create log file:" << logFilePath;
            qWarning() << "Error:" << m_file_Log.errorString();
        }
    }

    // 写入日志消息
    if (m_file_Log.isOpen()) {
        QString logEntry = QString("[%1] %2")
                          .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss.zzz"))
                          .arg(message);

        // 写入日志
        m_textStream_Out << logEntry << "\n";
        m_textStream_Out.flush(); // 确保立即写入磁盘

        // 同时输出到控制台（调试时有用）
        qDebug().noquote() << logEntry;
    } else {
        // 如果日志文件无法打开，至少输出到控制台
        qDebug() << "LOG:" << message;
    }
}

QString CDlgMain_MonitorWhiteCtrlProgram::getSystemUserName() {
    // 方法一：通过环境变量
    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    QString userName = env.value("USERNAME");  // Windows
    if (userName.isEmpty()) {
        userName = env.value("USER");  // Linux/macOS
    }

    if (!userName.isEmpty()) {
        return userName;
    }

    // 方法二：通过平台 API
#ifdef Q_OS_WIN
    #ifndef UNLEN
    #define UNLEN 256
    #endif
    wchar_t username[UNLEN + 1];
    DWORD size = UNLEN + 1;
    if (GetUserNameW(username, &size)) {
        return QString::fromWCharArray(username);
    }
#else
    char* username = getlogin();
    if (username) {
        return QString::fromLocal8Bit(username);
    }
#endif

    return QString("Unknown");
}

// ===============================
// 新增管理器初始化函数
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::initTrayManager()
{
    m_trayManager = new TrayManager(this, this);

    // 连接托盘信号到主窗口槽函数
    connect(m_trayManager, &TrayManager::clearWhiteListRequested,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onClearWhiteList);
    connect(m_trayManager, &TrayManager::syncWhiteListRequested,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onSyncWhiteList);

    // 连接白名单管理器到托盘管理器（如果白名单管理器已初始化）
    if (m_whiteListManager) {
        connect(m_whiteListManager, &WhiteListManager::whiteListUpdated,
                m_trayManager, &TrayManager::onWhiteListUpdated);
    }

    qDebug() << "TrayManager initialized";
}

void CDlgMain_MonitorWhiteCtrlProgram::initWhiteListManager()
{
    m_whiteListManager = new WhiteListManager(this);

    // 连接白名单信号
    connect(m_whiteListManager, &WhiteListManager::whiteListUpdated,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onWhiteListUpdated);
    connect(m_whiteListManager, &WhiteListManager::syncCompleted,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onSyncCompleted);

    qDebug() << "WhiteListManager initialized";
}

void CDlgMain_MonitorWhiteCtrlProgram::initUSBDeviceMonitor()
{
    m_usbDeviceMonitor = new USBDeviceMonitor(m_whiteListManager, this);

    // 连接USB设备信号
    connect(m_usbDeviceMonitor, &USBDeviceMonitor::whiteListUSBKeyDetected,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onUSBKeyDetected);

    // 启动USB设备监控
    m_usbDeviceMonitor->startMonitoring();

    qDebug() << "USBDeviceMonitor initialized";
}

void CDlgMain_MonitorWhiteCtrlProgram::initServiceManager()
{
    m_serviceManager = new ServiceManager(this);

    qDebug() << "ServiceManager initialized";
    qDebug() << "Service status:" << m_serviceManager->getServiceStatusString();
    qDebug() << "Auto start:" << (m_serviceManager->isAutoStartEnabled() ? "Enabled" : "Disabled");
}

void CDlgMain_MonitorWhiteCtrlProgram::updateWindowTitle()
{
    QString title = "显示器管控程序 v1.0";

    if (m_whiteListManager) {
        int whiteListCount = m_whiteListManager->getWhiteListCount();
        title += QString(" - 白名单: %1 个显示器").arg(whiteListCount);
    }

    if (m_edidManager) {
        QList<DisplayInfo> displays = m_edidManager->getAllDisplaysInfo();
        title += QString(" - 当前: %1 个显示器").arg(displays.size());
    }

    setWindowTitle(title);
}

// ===============================
// 窗口事件处理
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::closeEvent(QCloseEvent *event)
{
    // 检查是否有托盘图标
    if (m_trayManager && m_trayManager->isSystemTrayAvailable()) {
        // 隐藏到托盘而不是退出
        hide();
        if (m_trayManager) {
            m_trayManager->updateTrayStatus("后台运行");
        }
        event->ignore();

        WriteLog("主窗口隐藏到系统托盘");
    } else {
        // 没有托盘支持，正常退出
        event->accept();
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::changeEvent(QEvent *event)
{
    if (event->type() == QEvent::WindowStateChange) {
        if (isMinimized() && m_trayManager && m_trayManager->isSystemTrayAvailable()) {
            // 最小化时隐藏到托盘
            hide();
            event->ignore();
            return;
        }
    }

    QMainWindow::changeEvent(event);
}

// ===============================
// 托盘相关槽函数
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::onClearWhiteList()
{
    if (!m_whiteListManager) {
        return;
    }

    int currentCount = m_whiteListManager->getWhiteListCount();
    if (currentCount == 0) {
        QMessageBox::information(this, "白名单管理", "白名单已经为空。");
        return;
    }

    m_whiteListManager->clearWhiteList();

    WriteLog(QString("用户清空白名单，原有 %1 个条目").arg(currentCount));
    updateWindowTitle();

    qDebug() << "WhiteList cleared";
}

void CDlgMain_MonitorWhiteCtrlProgram::onSyncWhiteList()
{
    if (!m_whiteListManager) {
        return;
    }

    // 查找USB Key
    QStringList usbKeys = m_whiteListManager->findUSBKeys();

    if (usbKeys.isEmpty()) {
        QMessageBox::information(this, "白名单同步",
                                "未找到包含白名单文件的USB设备。\n\n"
                                "请确保USB设备中包含 monitor_whitelist.json 文件。");
        return;
    }

    if (usbKeys.size() == 1) {
        // 只有一个USB Key，直接同步
        QString usbPath = usbKeys.first();
        WriteLog(QString("开始同步USB Key白名单: %1").arg(usbPath));
        m_whiteListManager->syncFromUSBKey(usbPath);
    } else {
        // 多个USB Key，让用户选择
        QStringList items;
        for (const QString &usbPath : usbKeys) {
            items << QString("USB设备: %1").arg(usbPath);
        }

        bool ok;
        QString selectedItem = QInputDialog::getItem(this, "选择USB设备",
                                                    "发现多个包含白名单的USB设备，请选择：",
                                                    items, 0, false, &ok);
        if (ok && !selectedItem.isEmpty()) {
            int index = items.indexOf(selectedItem);
            if (index >= 0 && index < usbKeys.size()) {
                QString usbPath = usbKeys[index];
                WriteLog(QString("用户选择同步USB Key白名单: %1").arg(usbPath));
                m_whiteListManager->syncFromUSBKey(usbPath);
            }
        }
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::showMainWindow()
{
    show();
    raise();
    activateWindow();

    if (m_trayManager) {
        m_trayManager->updateTrayStatus("运行中");
    }

    WriteLog("主窗口显示");
}

void CDlgMain_MonitorWhiteCtrlProgram::hideToTray()
{
    if (m_trayManager && m_trayManager->isSystemTrayAvailable()) {
        hide();
        if (m_trayManager) {
            m_trayManager->updateTrayStatus("后台运行");
        }
        WriteLog("主窗口隐藏到托盘");
    }
}

// ===============================
// 其他槽函数
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::onWhiteListUpdated(int count)
{
    updateWindowTitle();
    WriteLog(QString("白名单已更新，当前包含 %1 个显示器").arg(count));

    qDebug() << "WhiteList updated, count:" << count;
}

void CDlgMain_MonitorWhiteCtrlProgram::onUSBKeyDetected(const QString &usbPath)
{
    WriteLog(QString("检测到包含白名单的USB Key: %1").arg(usbPath));

    // 可以在这里添加自动同步的逻辑
    // 或者显示通知让用户确认是否同步

    qDebug() << "WhiteList USB Key detected:" << usbPath;
}

void CDlgMain_MonitorWhiteCtrlProgram::onSyncCompleted(bool success, const QString &message)
{
    if (success) {
        QMessageBox::information(this, "同步完成", message);
        WriteLog(QString("白名单同步成功: %1").arg(message));
    } else {
        QMessageBox::warning(this, "同步失败", message);
        WriteLog(QString("白名单同步失败: %1").arg(message));
    }

    updateWindowTitle();
}
