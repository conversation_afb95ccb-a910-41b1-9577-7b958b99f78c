#include "DlgMain_MonitorWhiteCtrlProgram.h"
#include "ui_DlgMain_MonitorWhiteCtrlProgram.h"
#include "TrayManager.h"
#include "WhiteListManager.h"
#include "USBDeviceMonitor.h"
#include "ServiceManager.h"
#include <QApplication>
#include <QMessageBox>
#include <QInputDialog>
#include <QMutexLocker>
#include <QFileInfo>
#include <QDir>
#include <QCoreApplication>

// Windows显示器枚举回调函数
#ifdef Q_OS_WIN
BOOL CALLBACK MonitorEnumProc(HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData)
{
    Q_UNUSED(hdcMonitor)

    MONITORINFOEX monitorInfo;
    monitorInfo.cbSize = sizeof(MONITORINFOEX);

    // 获取显示器详细信息
    if (GetMonitorInfo(hMonitor, &monitorInfo)) {
        QString deviceName = QString::fromWCharArray(monitorInfo.szDevice);

        qDebug() << "显示器设备:" << deviceName;
        qDebug() << "  位置: (" << lprcMonitor->left << ", " << lprcMonitor->top << ")";
        qDebug() << "  尺寸:" << (lprcMonitor->right - lprcMonitor->left) << " x "
                  << (lprcMonitor->bottom - lprcMonitor->top) << " 像素";
        qDebug() << "  是否为主显示器:" << (monitorInfo.dwFlags & MONITORINFOF_PRIMARY ? "是" : "否");

        // 将显示器信息传递给调用者
        if (dwData) {
            QList<DisplayInfo> *displaysList = reinterpret_cast<QList<DisplayInfo>*>(dwData);

            // 查找对应的EDID信息
            for (DisplayInfo &info : *displaysList) {
                if (info.deviceName.contains(deviceName) || deviceName.contains(info.deviceName)) {
                    info.isPrimary = (monitorInfo.dwFlags & MONITORINFOF_PRIMARY) != 0;
                    break;
                }
            }
        }
    }
    return TRUE; // 返回 TRUE 继续枚举，返回 FALSE 停止枚举
}
#endif

// ===============================
// 主窗口类实现
// ===============================

CDlgMain_MonitorWhiteCtrlProgram::CDlgMain_MonitorWhiteCtrlProgram(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::CDlgMain_MonitorWhiteCtrlProgram),
    m_edidManager(nullptr),
    m_trayManager(nullptr),
    m_whiteListManager(nullptr),
    m_usbDeviceMonitor(nullptr),
    m_serviceManager(nullptr),
    m_isInitialized(false)
{
    ui->setupUi(this);

    // 设置窗口标题
    setWindowTitle("显示器管控程序 v1.0");

    // 记录程序启动
    WriteLog("显示器管控程序启动");
    WriteLog("测试中文编码：这是一条包含中文的测试日志");
    WriteLog("特殊字符测试：①②③④⑤ ★☆♠♥♦♣ αβγδε");

    // 按顺序初始化所有管理器
    initEDIDManager();
    initWhiteListManager();
    initUSBDeviceMonitor();
    initServiceManager();
    initTrayManager();

    // 显示所有显示器信息
    showDisplaysInfo();

    // 更新窗口标题
    updateWindowTitle();

    m_isInitialized = true;

    qDebug() << "主窗口初始化完成";
}

CDlgMain_MonitorWhiteCtrlProgram::~CDlgMain_MonitorWhiteCtrlProgram()
{
    WriteLog("显示器管控程序退出");

    // 按相反顺序清理管理器
    if (m_trayManager) {
        delete m_trayManager;
        m_trayManager = nullptr;
    }

    if (m_usbDeviceMonitor) {
        delete m_usbDeviceMonitor;
        m_usbDeviceMonitor = nullptr;
    }

    if (m_whiteListManager) {
        delete m_whiteListManager;
        m_whiteListManager = nullptr;
    }

    if (m_serviceManager) {
        delete m_serviceManager;
        m_serviceManager = nullptr;
    }

    if (m_edidManager) {
        delete m_edidManager;
        m_edidManager = nullptr;
    }

    delete ui;
}

void CDlgMain_MonitorWhiteCtrlProgram::initEDIDManager()
{
    m_edidManager = new EDIDManager(this);

    // 连接信号槽
    connect(m_edidManager, &EDIDManager::displaysChanged,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onDisplaysChanged);
}

void CDlgMain_MonitorWhiteCtrlProgram::refreshDisplaysInfo()
{
    if (m_edidManager) {
        m_edidManager->refreshDisplaysInfo();
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::showDisplaysInfo()
{
    if (!m_edidManager) return;

    QList<DisplayInfo> displays = m_edidManager->getAllDisplaysInfo();

    qDebug() << "\n========== 显示器EDID信息 ==========";
    qDebug() << "检测到" << displays.size() << "个显示器:";

    for (int i = 0; i < displays.size(); ++i) {
        const DisplayInfo &info = displays[i];

        qDebug() << "\n--- 显示器" << (i + 1) << "---";
        qDebug() << "设备名称:" << info.deviceName;
        qDebug() << "制造商:" << info.manufacturer;
        qDebug() << "产品代码:" << info.productCode;
        qDebug() << "序列号:" << info.serialNumber;
        qDebug() << "制造日期:" << "第" << info.manufactureWeek << "周，" << info.manufactureYear << "年";
        qDebug() << "EDID版本:" << info.edidVersion;
        qDebug() << "物理尺寸:" << info.physicalSize.width() << "x" << info.physicalSize.height() << "mm";
        qDebug() << "原生分辨率:" << info.nativeResolution.width() << "x" << info.nativeResolution.height();
        qDebug() << "是否为主显示器:" << (info.isPrimary ? "是" : "否");

        qDebug() << "支持的分辨率:";
        for (const QSize &res : info.supportedResolutions) {
            qDebug() << "  " << res.width() << "x" << res.height();
        }

        qDebug() << "原始EDID数据 (前32字节):" << info.rawEDID.left(32).toHex();

        // 记录日志
        WriteLog(QString("检测到显示器: %1 (%2 %3)")
                .arg(info.deviceName)
                .arg(info.manufacturer)
                .arg(info.productCode));
    }

    qDebug() << "=====================================\n";
}

void CDlgMain_MonitorWhiteCtrlProgram::onDisplaysChanged()
{
    qDebug() << "显示器配置发生变化，重新加载信息...";
    showDisplaysInfo();

    WriteLog("显示器配置发生变化");
}

void CDlgMain_MonitorWhiteCtrlProgram::WriteLog(const QString &message)
{
    QMutexLocker locker(&m_mutex_Log);  // 使用RAII方式管理锁

    if (!m_file_Log.isOpen())
    {
        // 获取程序所在目录
        QString appDirPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath();
        QString logDirPath = QDir(appDirPath).filePath("Log");

        // 检查Log文件夹是否存在，不存在则创建
        QDir logDir(logDirPath);
        if (!logDir.exists()) {
            if (logDir.mkpath(logDirPath)) {
                qDebug() << QString::fromUtf8("Log\xe7\x9b\xae\xe5\xbd\x95\xe5\x88\x9b\xe5\xbb\xba\xe6\x88\x90\xe5\x8a\x9f:") << logDirPath;
            } else {
                qWarning() << QString::fromUtf8("Log\xe7\x9b\xae\xe5\xbd\x95\xe5\x88\x9b\xe5\xbb\xba\xe5\xa4\xb1\xe8\xb4\xa5:") << logDirPath;
                // 如果创建失败，使用程序目录作为备选
                logDirPath = appDirPath;
            }
        } else {
            qDebug() << QString::fromUtf8("Log\xe7\x9b\xae\xe5\xbd\x95\xe5\xb7\xb2\xe5\xad\x98\xe5\x9c\xa8:") << logDirPath;
        }

        // 生成日志文件名（包含时间戳以避免冲突）
        QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd");
        QString logFileName = QString("%1_MonitorCtrl.log").arg(timestamp);
        QString logFilePath = QDir(logDirPath).filePath(logFileName);

        // 设置日志文件路径并打开
        m_file_Log.setFileName(logFilePath);
        if (m_file_Log.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
            m_textStream_Out.setDevice(&m_file_Log);

            // 设置文本流编码为UTF-8，解决中文乱码问题
            m_textStream_Out.setCodec("UTF-8");

            // 写入UTF-8 BOM标记，确保文件被正确识别为UTF-8编码
            m_textStream_Out << QString::fromUtf8("\xEF\xBB\xBF");

            // 写入日志文件头信息
            m_textStream_Out << "========================================\n";
            m_textStream_Out << "显示器管控程序日志文件\n";
            m_textStream_Out << "启动时间: " << QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss.zzz") << "\n";
            m_textStream_Out << "程序版本: v1.0\n";
            m_textStream_Out << "用户名: " << getSystemUserName() << "\n";
            m_textStream_Out << "日志文件: " << logFilePath << "\n";
            m_textStream_Out << "========================================\n";
            m_textStream_Out.flush();

            qDebug() << QString::fromUtf8("日志文件创建成功:") << logFilePath;
        } else {
            qWarning() << QString::fromUtf8("无法创建日志文件:") << logFilePath;
            qWarning() << QString::fromUtf8("错误信息:") << m_file_Log.errorString();
        }
    }

    // 写入日志消息
    if (m_file_Log.isOpen()) {
        // 确保编码设置正确
        ensureLogFileEncoding();

        QString logEntry = QString("[%1] %2")
                          .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss.zzz"))
                          .arg(message);

        // 使用UTF-8编码写入日志
        m_textStream_Out << logEntry << "\n";
        m_textStream_Out.flush(); // 确保立即写入磁盘

        // 同时输出到控制台（调试时有用）
        qDebug().noquote() << logEntry;
    } else {
        // 如果日志文件无法打开，至少输出到控制台
        qDebug() << "LOG:" << message;
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::ensureLogFileEncoding()
{
    // 确保文本流使用UTF-8编码
    if (m_textStream_Out.device() && m_textStream_Out.codec() != QTextCodec::codecForName("UTF-8")) {
        m_textStream_Out.setCodec("UTF-8");
    }
}

QString CDlgMain_MonitorWhiteCtrlProgram::getSystemUserName() {
    // 方法一：通过环境变量
    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    QString userName = env.value("USERNAME");  // Windows
    if (userName.isEmpty()) {
        userName = env.value("USER");  // Linux/macOS
    }

    if (!userName.isEmpty()) {
        return userName;
    }

    // 方法二：通过平台 API
#ifdef Q_OS_WIN
    #ifndef UNLEN
    #define UNLEN 256
    #endif
    wchar_t username[UNLEN + 1];
    DWORD size = UNLEN + 1;
    if (GetUserNameW(username, &size)) {
        return QString::fromWCharArray(username);
    }
#else
    char* username = getlogin();
    if (username) {
        return QString::fromLocal8Bit(username);
    }
#endif

    return QString("Unknown");
}

// ===============================
// 新增管理器初始化函数
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::initTrayManager()
{
    m_trayManager = new TrayManager(this, this);

    // 连接托盘信号到主窗口槽函数
    connect(m_trayManager, &TrayManager::clearWhiteListRequested,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onClearWhiteList);
    connect(m_trayManager, &TrayManager::syncWhiteListRequested,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onSyncWhiteList);

    // 连接白名单管理器到托盘管理器（如果白名单管理器已初始化）
    if (m_whiteListManager) {
        connect(m_whiteListManager, &WhiteListManager::whiteListUpdated,
                m_trayManager, &TrayManager::onWhiteListUpdated);
    }

    qDebug() << QString::fromUtf8("\xe6\x89\x98\xe7\x9b\x98\xe7\xae\xa1\xe7\x90\x86\xe5\x99\xa8\xe5\x88\x9d\xe5\xa7\x8b\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90");
}

void CDlgMain_MonitorWhiteCtrlProgram::initWhiteListManager()
{
    m_whiteListManager = new WhiteListManager(this);

    // 连接白名单信号
    connect(m_whiteListManager, &WhiteListManager::whiteListUpdated,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onWhiteListUpdated);
    connect(m_whiteListManager, &WhiteListManager::syncCompleted,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onSyncCompleted);

    qDebug() << QString::fromUtf8("\xe7\x99\xbd\xe5\x90\x8d\xe5\x8d\x95\xe7\xae\xa1\xe7\x90\x86\xe5\x99\xa8\xe5\x88\x9d\xe5\xa7\x8b\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90");
}

void CDlgMain_MonitorWhiteCtrlProgram::initUSBDeviceMonitor()
{
    m_usbDeviceMonitor = new USBDeviceMonitor(m_whiteListManager, this);

    // 连接USB设备信号
    connect(m_usbDeviceMonitor, &USBDeviceMonitor::whiteListUSBKeyDetected,
            this, &CDlgMain_MonitorWhiteCtrlProgram::onUSBKeyDetected);

    // 启动USB设备监控
    m_usbDeviceMonitor->startMonitoring();

    qDebug() << QString::fromUtf8("USB\xe8\xae\xbe\xe5\xa4\x87\xe7\x9b\x91\xe6\x8e\xa7\xe5\x99\xa8\xe5\x88\x9d\xe5\xa7\x8b\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90");
}

void CDlgMain_MonitorWhiteCtrlProgram::initServiceManager()
{
    m_serviceManager = new ServiceManager(this);

    qDebug() << QString::fromUtf8("\xe6\x9c\x8d\xe5\x8a\xa1\xe7\xae\xa1\xe7\x90\x86\xe5\x99\xa8\xe5\x88\x9d\xe5\xa7\x8b\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90");
    qDebug() << QString::fromUtf8("\xe6\x9c\x8d\xe5\x8a\xa1\xe7\x8a\xb6\xe6\x80\x81:") << m_serviceManager->getServiceStatusString();
    qDebug() << QString::fromUtf8("\xe5\xbc\x80\xe6\x9c\xba\xe8\x87\xaa\xe5\x90\x9a\xe5\x8a\xa8:") << (m_serviceManager->isAutoStartEnabled() ? QString::fromUtf8("\xe5\xb7\xb2\xe5\x90\xaf\xe7\x94\xa8") : QString::fromUtf8("\xe6\x9c\xaa\xe5\x90\xaf\xe7\x94\xa8"));
}

void CDlgMain_MonitorWhiteCtrlProgram::updateWindowTitle()
{
    QString title = "显示器管控程序 v1.0";

    if (m_whiteListManager) {
        int whiteListCount = m_whiteListManager->getWhiteListCount();
        title += QString(" - 白名单: %1 个显示器").arg(whiteListCount);
    }

    if (m_edidManager) {
        QList<DisplayInfo> displays = m_edidManager->getAllDisplaysInfo();
        title += QString(" - 当前: %1 个显示器").arg(displays.size());
    }

    setWindowTitle(title);
}

// ===============================
// 窗口事件处理
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::closeEvent(QCloseEvent *event)
{
    // 检查是否有托盘图标
    if (m_trayManager && m_trayManager->isSystemTrayAvailable()) {
        // 隐藏到托盘而不是退出
        hide();
        if (m_trayManager) {
            m_trayManager->updateTrayStatus("后台运行");
        }
        event->ignore();

        WriteLog("主窗口隐藏到系统托盘");
    } else {
        // 没有托盘支持，正常退出
        event->accept();
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::changeEvent(QEvent *event)
{
    if (event->type() == QEvent::WindowStateChange) {
        if (isMinimized() && m_trayManager && m_trayManager->isSystemTrayAvailable()) {
            // 最小化时隐藏到托盘
            hide();
            event->ignore();
            return;
        }
    }

    QMainWindow::changeEvent(event);
}

// ===============================
// 托盘相关槽函数
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::onClearWhiteList()
{
    if (!m_whiteListManager) {
        return;
    }

    int currentCount = m_whiteListManager->getWhiteListCount();
    if (currentCount == 0) {
        QMessageBox::information(this, "白名单管理", "白名单已经为空。");
        return;
    }

    m_whiteListManager->clearWhiteList();

    WriteLog(QString("用户清空白名单，原有 %1 个条目").arg(currentCount));
    updateWindowTitle();

    qDebug() << "白名单已清空";
}

void CDlgMain_MonitorWhiteCtrlProgram::onSyncWhiteList()
{
    if (!m_whiteListManager) {
        return;
    }

    // 查找USB Key
    QStringList usbKeys = m_whiteListManager->findUSBKeys();

    if (usbKeys.isEmpty()) {
        QMessageBox::information(this, "白名单同步",
                                "未找到包含白名单文件的USB设备。\n\n"
                                "请确保USB设备中包含 monitor_whitelist.json 文件。");
        return;
    }

    if (usbKeys.size() == 1) {
        // 只有一个USB Key，直接同步
        QString usbPath = usbKeys.first();
        WriteLog(QString("开始同步USB Key白名单: %1").arg(usbPath));
        m_whiteListManager->syncFromUSBKey(usbPath);
    } else {
        // 多个USB Key，让用户选择
        QStringList items;
        for (const QString &usbPath : usbKeys) {
            items << QString("USB设备: %1").arg(usbPath);
        }

        bool ok;
        QString selectedItem = QInputDialog::getItem(this, "选择USB设备",
                                                    "发现多个包含白名单的USB设备，请选择：",
                                                    items, 0, false, &ok);
        if (ok && !selectedItem.isEmpty()) {
            int index = items.indexOf(selectedItem);
            if (index >= 0 && index < usbKeys.size()) {
                QString usbPath = usbKeys[index];
                WriteLog(QString("用户选择同步USB Key白名单: %1").arg(usbPath));
                m_whiteListManager->syncFromUSBKey(usbPath);
            }
        }
    }
}

void CDlgMain_MonitorWhiteCtrlProgram::showMainWindow()
{
    show();
    raise();
    activateWindow();

    if (m_trayManager) {
        m_trayManager->updateTrayStatus("运行中");
    }

    WriteLog("主窗口显示");
}

void CDlgMain_MonitorWhiteCtrlProgram::hideToTray()
{
    if (m_trayManager && m_trayManager->isSystemTrayAvailable()) {
        hide();
        if (m_trayManager) {
            m_trayManager->updateTrayStatus("后台运行");
        }
        WriteLog("主窗口隐藏到托盘");
    }
}

// ===============================
// 其他槽函数
// ===============================

void CDlgMain_MonitorWhiteCtrlProgram::onWhiteListUpdated(int count)
{
    updateWindowTitle();
    WriteLog(QString("白名单已更新，当前包含 %1 个显示器").arg(count));

    qDebug() << "白名单更新，当前数量:" << count;
}

void CDlgMain_MonitorWhiteCtrlProgram::onUSBKeyDetected(const QString &usbPath)
{
    WriteLog(QString("检测到包含白名单的USB Key: %1").arg(usbPath));

    // 可以在这里添加自动同步的逻辑
    // 或者显示通知让用户确认是否同步

    qDebug() << "检测到白名单USB Key:" << usbPath;
}

void CDlgMain_MonitorWhiteCtrlProgram::onSyncCompleted(bool success, const QString &message)
{
    if (success) {
        QMessageBox::information(this, "同步完成", message);
        WriteLog(QString("白名单同步成功: %1").arg(message));
    } else {
        QMessageBox::warning(this, "同步失败", message);
        WriteLog(QString("白名单同步失败: %1").arg(message));
    }

    updateWindowTitle();
}
